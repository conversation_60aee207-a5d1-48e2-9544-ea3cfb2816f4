import type { Service, ServiceFormData, Event, EventFormData, ServiceCategory } from '~/types/service.types'

// Currency formatting for Ghana Cedis
export const formatCurrency = (amount: number): string => {
  return `₵${amount.toFixed(2)}`
}

// Duration formatting
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}min`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`
}

// API error handling
export const handleApiError = (error: any, defaultMessage: string): string => {
  if (error?.data?.message) {
    return error.data.message
  }
  if (error?.message) {
    return error.message
  }
  return defaultMessage
}

// Service API functions
export const serviceApi = {
  // Helper to get auth headers
  getAuthHeaders() {
    const { $auth } = useNuxtApp()
    return {
      'Authorization': `Bearer ${$auth.value?.token}`,
      'Accept': 'application/json'
    }
  },

  // Helper to get base URL
  getBaseUrl() {
    const config = useRuntimeConfig()
    return config.public.apiBase
  },

  async getAll(): Promise<Service[]> {
    try {
      return await $fetch<Service[]>(`${this.getBaseUrl()}/services`, {
        headers: this.getAuthHeaders()
      })
    } catch (error) {
      console.error('Error fetching all services:', error)
      throw error
    }
  },

  async getById(id: string): Promise<Service> {
    try {
      return await $fetch<Service>(`${this.getBaseUrl()}/services/${id}`, {
        headers: this.getAuthHeaders()
      })
    } catch (error) {
      console.error(`Error fetching service ${id}:`, error)
      throw error
    }
  },

  async create(data: ServiceFormData, imageFile?: File): Promise<Service> {
    try {
      const formData = new FormData()
      formData.append('name', data.name)
      formData.append('price', data.price.toString())

      if (data.description) formData.append('description', data.description)
      if (data.categoryId) formData.append('categoryId', data.categoryId)
      if (imageFile) formData.append('image', imageFile)

      return await $fetch<Service>(`${this.getBaseUrl()}/services`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${useNuxtApp().$auth.value?.token}`
        }
      })
    } catch (error) {
      console.error('Error creating service:', error)
      throw error
    }
  },

  async update(id: string, data: Partial<ServiceFormData>, imageFile?: File): Promise<Service> {
    try {
      const formData = new FormData()

      // Only append defined values
      if (data.name !== undefined) formData.append('name', data.name)
      if (data.price !== undefined) formData.append('price', data.price.toString())
      if (data.description !== undefined) formData.append('description', data.description)
      if (data.categoryId !== undefined) formData.append('categoryId', data.categoryId)
      if (imageFile) formData.append('image', imageFile)

      return await $fetch<Service>(`${this.getBaseUrl()}/services/${id}`, {
        method: 'PUT',
        body: formData,
        headers: {
          'Authorization': `Bearer ${useNuxtApp().$auth.value?.token}`
        }
      })
    } catch (error) {
      console.error(`Error updating service ${id}:`, error)
      throw error
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await $fetch(`${this.getBaseUrl()}/services/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      })
    } catch (error) {
      console.error(`Error deleting service ${id}:`, error)
      throw error
    }
  }
}

// Event API functions
export const eventApi = {
  async getAll(): Promise<Event[]> {
    const config = useRuntimeConfig()
    return await $fetch<Event[]>(`${config.public.apiBase}/events`)
  },

  async getTenant(): Promise<Event[]> {
    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    return await $fetch<Event[]>(`${config.public.apiBase}/events/tenant`, {
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`,
        'Accept': 'application/json'
      }
    })
  },

  async getById(id: string): Promise<Event> {
    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    return await $fetch<Event>(`${config.public.apiBase}/events/${id}`, {
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`,
        'Accept': 'application/json'
      }
    })
  },

  async create(data: EventFormData, imageFile?: File): Promise<Event> {
    const formData = new FormData()
    formData.append('title', data.title)
    formData.append('description', data.description)
    formData.append('startDate', data.startDate)
    formData.append('endDate', data.endDate)
    formData.append('startTime', data.startTime)
    formData.append('endTime', data.endTime)
    formData.append('isActive', data.isActive.toString())

    if (data.location) formData.append('location', data.location)
    if (data.category) formData.append('category', data.category)
    if (data.maxAttendees) formData.append('maxAttendees', data.maxAttendees.toString())
    if (data.price) formData.append('price', data.price.toString())
    if (imageFile) formData.append('image', imageFile)

    return await $apiFetch<Event>('/events', formData)
  },

  async update(id: string, data: Partial<EventFormData>, imageFile?: File): Promise<Event> {
    const formData = new FormData()

    if (data.title !== undefined) formData.append('title', data.title)
    if (data.description !== undefined) formData.append('description', data.description)
    if (data.startDate !== undefined) formData.append('startDate', data.startDate)
    if (data.endDate !== undefined) formData.append('endDate', data.endDate)
    if (data.startTime !== undefined) formData.append('startTime', data.startTime)
    if (data.endTime !== undefined) formData.append('endTime', data.endTime)
    if (data.location !== undefined) formData.append('location', data.location)
    if (data.category !== undefined) formData.append('category', data.category)
    if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString())
    if (data.maxAttendees !== undefined) formData.append('maxAttendees', data.maxAttendees.toString())
    if (data.price !== undefined) formData.append('price', data.price.toString())
    if (imageFile) formData.append('image', imageFile)

    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    return await $fetch<Event>(`${config.public.apiBase}/events/${id}`, {
      method: 'PUT',
      body: formData,
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`
      }
    })
  },

  async delete(id: string): Promise<void> {
    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    await $fetch(`${config.public.apiBase}/events/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`,
        'Accept': 'application/json'
      }
    })
  }
}

// Search and filter utilities
export const filterServices = (services: Service[], query: string): Service[] => {
  if (!query.trim()) return services
  const searchTerm = query.toLowerCase()
  return services.filter(service =>
    service.name.toLowerCase().includes(searchTerm) ||
    service.description.toLowerCase().includes(searchTerm) ||
    service.category?.name?.toLowerCase().includes(searchTerm) ||
    service.categoryId?.toLowerCase().includes(searchTerm)
  )
}

export const filterEvents = (events: Event[], query: string): Event[] => {
  if (!query.trim()) return events
  const searchTerm = query.toLowerCase()
  return events.filter(event => 
    event.title.toLowerCase().includes(searchTerm) ||
    event.description.toLowerCase().includes(searchTerm) ||
    event.location?.toLowerCase().includes(searchTerm) ||
    event.category?.toLowerCase().includes(searchTerm)
  )
}

// Category API functions
export const categoryApi = {
  async getAll(): Promise<ServiceCategory[]> {
    const config = useRuntimeConfig()
    return await $fetch<ServiceCategory[]>(`${config.public.apiBase}/categories`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    })
  },

  async create(data: { name: string; description?: string }): Promise<ServiceCategory> {
    return await $apiFetch<ServiceCategory>('/categories', data)
  },

  async update(id: string, data: { name?: string; description?: string }): Promise<ServiceCategory> {
    return await $apiFetch<ServiceCategory>(`/categories/${id}`, data, { method: 'PUT' })
  },

  async delete(id: string): Promise<void> {
    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    await $fetch(`${config.public.apiBase}/categories/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`,
        'Accept': 'application/json'
      }
    })
  }
}
